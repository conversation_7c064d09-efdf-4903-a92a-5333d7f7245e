#!/bin/bash

args=("$@")
echo "args: ${args[@]}"
ADMIN_AUTH="${args[0]}"

if [ -z "$ADMIN_AUTH" ]; then
  echo "Error: ADMIN_AUTH is not set. Please provide the authorization token."
  exit 1
fi

sudo apt-get install webhook
sudo echo "
[
  {
    "id": "redeploy-webhook",
    "execute-command": "./redeploy.sh",
    "command-working-directory": "./",
    "incoming-payload-content-type": "application/json",
    "trigger-rule": {
        "and": [
        
            "match":
            {
                "parameter":
                {
                    "source": "header",
                    "name": "Authorization"
                },
                "type": "value",
                "value": "$ADMIN_AUTH"
            }
        ]
    }
  }
]" > ./hooks.json

webhook -hooks ./hooks.json -verbose -port 2500
