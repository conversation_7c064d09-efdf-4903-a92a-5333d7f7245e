
[
  {
    id: redeploy-webhook,
    execute-command: ./redeploy.sh,
    command-working-directory: ./,
    incoming-payload-content-type: application/json,
    trigger-rule: {
        and: [
        
            match:
            {
                parameter:
                {
                    source: header,
                    name: Authorization
                },
                type: value,
                value: test-webhook-token
            }
        ]
    }
  }
]
