package main

import (
	. "bridge"
	"fmt"
	"math"
	"strings"
	"time"
)

// tickLogger is a simple in-memory logger used per tick; caller clears between ticks.
type tickLogger struct {
	lines []string
	max   int
}

func newTickLogger(max int) *tickLogger { return &tickLogger{lines: make([]string, 0, 128), max: max} }

func (l *tickLogger) Log(args ...interface{}) {
	// fmt.Println("tickLogger.Log:", args)
	if l == nil {
		return
	}
	msg := fmt.Sprintln(args...)
	l.lines = append(l.lines, strings.TrimRight(msg, "\n"))
	if len(l.lines) > l.max {
		l.lines = l.lines[len(l.lines)-l.max:]
	}
}

// Config is a snake_case config holder populated by MapToStruct from a map.
// See env.json for all fields. This struct includes only core fields used in this
// initial scaffold; you can extend it incrementally.
type Config struct {
	Symbol                string
	Rate                  float64
	Remote_ticking        bool
	Currency_maps         []string
	Dry_run               bool
	Min_trade_interval_ms int

	Data struct {
		Ohlc struct {
			Timeframe   string
			Warmup_bars int
			Aggregation string
		}
		Volatility struct {
			Estimator   string
			Ewma_lambda float64
			Min_vol     float64
		}
	}

	Portfolio struct {
		Symbols            []string
		Gross_exposure_cap float64
		Per_symbol_lot_cap float64
	}

	Strategies struct {
		Trend_momentum struct {
			Enabled   bool
			Weight    float64
			Threshold float64
		}
		Vol_breakout struct {
			Enabled      bool
			Weight       float64
			Donchian_len int
			Atr_multiple float64
		}
		Carry_fx struct {
			Enabled    bool
			Weight     float64
			Carry_bias float64
		}
		Mean_reversion struct {
			Enabled    bool
			Weight     float64
			Rsi_period int
			Rsi_lower  float64
			Rsi_upper  float64
		}
	}

	Ensemble struct {
		Combine   string
		Cap       float64
		Threshold float64
	}

	Sizing struct {
		Mode                  string
		Target_vol_annualized float64
		Fixed_lot             float32
		Risk_percent          float32
		Reverse               bool
	}

	Risk struct {
		Max_aggregate_lots     float32
		Max_concurrent_trades  int
		Max_daily_drawdown_pct float32
		Max_daily_trades       int
		Stop_loss_pips         float64
		Take_profit_pips       float64
		Risk_per_trade_pct     float64 // % of account to risk per trade
		Max_position_size_pct  float64 // max % of account per position
		Dynamic_stops          bool    // use ATR-based stops
		Atr_stop_multiple      float64 // ATR multiplier for stops
		Equity_stop_abs        float32
		Equity_stop_pct        float32
		Symbol_whitelist       []string
		Symbol_blacklist       []string
		Time_windows           []string
	}

	Execution struct {
		Order_type          string
		Max_slippage_points int
		Time_in_force       string
		Stop_loss           struct {
			Mode         string
			Atr_multiple float64
			Fixed_points int
		}
		Take_profit struct {
			Rr_multiple  float64
			Fixed_points int
		}
		Trailing_stop struct {
			Enabled      bool
			Atr_multiple float64
		}
	}
}

var config Map = Map{}

// Minimal indicator state placeholders
type Bar struct {
	O, H, L, C float64
	T          int64
}

// EWMA vol estimator
func ewmaVol(prev, ret, lambda float64, minVol float64) float64 {
	v := lambda*prev + (1-lambda)*ret*ret
	if v < minVol*minVol {
		v = minVol * minVol
	}
	return math.Sqrt(v)
}

// calculateRiskBasedVolume calculates position size based on risk management
func calculateRiskBasedVolume(o Options, c Config, atrValue float64, currentPrice float64) float32 {
	// Use initial balance as proxy for equity (simplified for now)
	equity := 1000.0 // default account size
	if o.Simulated {
		// In simulation, use conservative sizing
		equity = 1000.0
	}

	if equity <= 0 {
		return 0.01
	}

	// Risk per trade as % of account
	riskPct := c.Risk.Risk_per_trade_pct
	if riskPct <= 0 {
		riskPct = 1.0 // default 1%
	}
	if riskPct > 5.0 {
		riskPct = 5.0 // cap at 5%
	}

	// Calculate stop loss distance
	var stopDistance float64
	if c.Risk.Dynamic_stops && atrValue > 0 {
		// Use ATR-based stops
		atrMultiple := c.Risk.Atr_stop_multiple
		if atrMultiple <= 0 {
			atrMultiple = 2.0
		}
		stopDistance = atrValue * atrMultiple
	} else {
		// Use fixed pip stops
		stopPips := c.Risk.Stop_loss_pips
		if stopPips <= 0 {
			stopPips = 20
		}
		stopDistance = stopPips * 0.0001 // convert pips to price
	}

	if stopDistance <= 0 {
		return 0.01
	}

	// Calculate position size based on risk
	riskAmount := equity * (riskPct / 100.0)
	contractSize := 100000.0 // standard lot size
	volume := riskAmount / (stopDistance * contractSize)

	// Apply maximum position size limit
	maxPosPct := c.Risk.Max_position_size_pct
	if maxPosPct <= 0 {
		maxPosPct = 10.0 // default 10% of account
	}
	maxVolume := equity * (maxPosPct / 100.0) / (currentPrice * contractSize)

	if volume > maxVolume {
		volume = maxVolume
	}

	// Ensure minimum and maximum bounds
	if volume < 0.01 {
		volume = 0.01
	}
	if volume > 1.0 {
		volume = 1.0
	}

	return float32(volume)
}

func main() {
	tl := newTickLogger(500)
	// optional startup message; will be flushed with first tick
	tl.Log("[alpha] starting bot main()")

	lastTradeAt := time.Time{}
	var actedBarTs int64 = 0
	// simple bar aggregator state
	var curBar *Bar
	var bars []Bar
	var initialized bool = false
	// indicators - will be initialized after fetching historical data
	var smaFast *SMA
	var smaSlow *SMA
	var atr *ATR
	// ewma variance for vol targeting
	var ewmaVar float64

	// Function to initialize indicators with historical data
	initializeIndicators := func(o Options, c Config) error {
		if initialized {
			return nil
		}

		symbol := c.Symbol
		if symbol == "" {
			symbol = "EURUSD"
		}
		mappedSymbol := applyCurrencyMaps(symbol, c.Currency_maps)

		timeframe := c.Data.Ohlc.Timeframe
		if timeframe == "" {
			timeframe = "M1"
		}

		warmupBars := c.Data.Ohlc.Warmup_bars
		if warmupBars <= 0 {
			warmupBars = 200 // default warmup
		}

		// In simulation mode, skip historical fetch as it's not needed
		if o.Simulated {
			tl.Log("[alpha] Simulation mode - initializing indicators without historical data")
			smaFast = NewSMA(20)
			smaSlow = NewSMA(100)
			atr = NewATR(14)
			initialized = true
			return nil
		}

		// Fetch historical candles for indicator warmup (live mode only)
		tl.Log("[alpha] Fetching historical candles for warmup...")
		resp, err := o.Client.GetTicksFrom(o.Ctx, &GetTicksFromRequest{
			Symbol:    mappedSymbol,
			StartDate: time.Now().UnixMilli(), // Start from current time
			Length:    int64(warmupBars),
			Timeframe: &timeframe,
		})

		if err != nil || resp == nil || len(resp.Ticks) < 10 {
			tl.Log("[alpha] Failed to fetch historical data, using defaults")
			// Initialize with default periods
			smaFast = NewSMA(20)
			smaSlow = NewSMA(100)
			atr = NewATR(14)
			initialized = true
			return nil
		}

		// Initialize indicators
		smaFast = NewSMA(20)
		smaSlow = NewSMA(100)
		atr = NewATR(14)

		// Warm up indicators with historical data
		tl.Log("[alpha] Warming up indicators with", len(resp.Ticks), "historical candles")
		for _, candle := range resp.Ticks {
			price := float64(candle.Close)
			smaFast.Add(price)
			smaSlow.Add(price)
			atr.Add(float64(candle.High), float64(candle.Low), price)

			// Also populate bars array
			bars = append(bars, Bar{
				O: float64(candle.Open),
				H: float64(candle.High),
				L: float64(candle.Low),
				C: float64(candle.Close),
				T: candle.Time,
			})
		}

		tl.Log("[alpha] Indicators initialized with", len(bars), "historical bars")
		tl.Log("[alpha] Fast SMA:", smaFast.Value(), "Slow SMA:", smaSlow.Value(), "ATR:", atr.Value())
		initialized = true
		return nil
	}

	var fn Callback = func(o Options, tick *TickType) []string {
		// reset per-tick buffer
		tl.lines = tl.lines[:0]
		tl.Log("[alpha] callback invoked")
		c := Config{}
		MapToStruct(config, &c)

		// Initialize indicators on first call
		if !initialized {
			if err := initializeIndicators(o, c); err != nil {
				tl.Log("[alpha] Failed to initialize indicators:", err)
			}
		}

		if o.Simulated {
			tl.Log("[alpha] simulated mode enabled")
		}

		if tick == nil {
			tl.Log("[alpha] tick is nil, returning")
			out := append([]string(nil), tl.lines...)
			tl.lines = tl.lines[:0]
			return out
		}
		tl.Log("[alpha] tick:", tick.Time, "ask:", tick.Ask, "bid:", tick.Bid)

		// Throttle decisions
		if !lastTradeAt.IsZero() && o.Simulated == false && time.Since(lastTradeAt) < time.Duration(c.Min_trade_interval_ms)*time.Millisecond {
			tl.Log("[alpha] throttled, lastTradeAt:", lastTradeAt.UnixMilli(), "elapsed(ms):", time.Since(lastTradeAt).Milliseconds(), "minInterval(ms):", c.Min_trade_interval_ms)
			out := append([]string(nil), tl.lines...)
			tl.lines = tl.lines[:0]
			return out
		}

		symbol := c.Symbol
		if symbol == "" {
			symbol = "EURUSD"
			tl.Log("[alpha] default symbol applied:", symbol)
		}

		// compute mapped symbol once
		mappedSymbol := applyCurrencyMaps(symbol, c.Currency_maps)
		tl.Log("[alpha] mappedSymbol:", mappedSymbol)

		// aggregate ticks into bars
		price := float64((tick.Ask + tick.Bid) / 2)
		ts := time.UnixMilli(tick.Time)
		bucket := bucketStart(ts, c.Data.Ohlc.Timeframe)
		if curBar == nil || curBar.T != bucket.UnixMilli() {
			if curBar != nil {
				// finalize previous bar
				bars = append(bars, *curBar)
				if len(bars) > 5000 {
					bars = bars[len(bars)-3000:]
				}
				// feed indicators from closed bar (only if initialized)
				if initialized && smaFast != nil && smaSlow != nil && atr != nil {
					smaFast.Add(curBar.C)
					smaSlow.Add(curBar.C)
					atr.Add(curBar.H, curBar.L, curBar.C)
				}
				// update ewma variance for vol targeting
				if len(bars) >= 2 {
					r := bars[len(bars)-1].C/bars[len(bars)-2].C - 1.0
					lambda := c.Data.Volatility.Ewma_lambda
					if lambda <= 0 {
						lambda = 0.94
					}
					ewmaVar = lambda*ewmaVar + (1-lambda)*r*r
					tl.Log("[alpha] closed bar:", curBar.T, "O:", curBar.O, "H:", curBar.H, "L:", curBar.L, "C:", curBar.C, "ret:", r, "ewmaVar:", ewmaVar)
				}
			}
			// start new bar
			curBar = &Bar{O: price, H: price, L: price, C: price, T: bucket.UnixMilli()}
			tl.Log("[alpha] new bar started @", curBar.T, "OHL C:", curBar.O, curBar.H, curBar.L, curBar.C)
		} else {
			if price > curBar.H {
				curBar.H = price
				tl.Log("[alpha] bar H updated:", curBar.H)
			}
			if price < curBar.L {
				curBar.L = price
				tl.Log("[alpha] bar L updated:", curBar.L)
			}
			curBar.C = price
			tl.Log("[alpha] bar C updated:", curBar.C)
		}

		// Check if indicators are ready
		if !initialized || smaFast == nil || smaSlow == nil || atr == nil {
			tl.Log("[alpha] indicators not ready yet")
			out := append([]string(nil), tl.lines...)
			tl.lines = tl.lines[:0]
			return out
		}

		// Safety check for bars array
		if len(bars) == 0 {
			tl.Log("[alpha] no bars available yet")
			out := append([]string(nil), tl.lines...)
			tl.lines = tl.lines[:0]
			return out
		}

		// trailing stop management (runs every tick after warmup)
		if c.Execution.Trailing_stop.Enabled {
			tl.Log("[alpha] manageTrailing enabled, atr:", atr.Value(), "mult:", c.Execution.Trailing_stop.Atr_multiple, "price:", price)
			manageTrailing(o, mappedSymbol, atr.Value(), c.Execution.Trailing_stop.Atr_multiple, price)
		}

		// act at most once per closed bar
		lastClosedTs := bars[len(bars)-1].T
		if actedBarTs == lastClosedTs {
			tl.Log("[alpha] already acted on bar:", actedBarTs)
			out := append([]string(nil), tl.lines...)
			tl.lines = tl.lines[:0]
			return out
		}

		// compute individual strategy signals
		closeVal := bars[len(bars)-1].C
		trendSig := 0.0
		if c.Strategies.Trend_momentum.Enabled {
			fast := smaFast.Value()
			slow := smaSlow.Value()
			tl.Log("[alpha] trend check: enabled=true, fast=", fast, "slow=", slow)
			if fast > 0 && slow > 0 {
				edge := (fast/slow - 1.0)
				thr := c.Strategies.Trend_momentum.Threshold
				if thr <= 0 {
					thr = 0.002 // More reasonable default
				}
				if edge > thr {
					trendSig = 1
				} else if edge < -thr {
					trendSig = -1
				}
				tl.Log("[alpha] trend: fast:", fast, "slow:", slow, "edge:", edge, "thr:", thr, "sig:", trendSig)
			} else {
				tl.Log("[alpha] trend: indicators not ready, fast=", fast, "slow=", slow)
			}
		} else {
			tl.Log("[alpha] trend strategy disabled")
		}

		volSig := 0.0
		if c.Strategies.Vol_breakout.Enabled {
			n := c.Strategies.Vol_breakout.Donchian_len
			if n <= 0 {
				n = 20
			}
			hi, lo := donchian(bars, n)
			a := atr.Value()
			m := c.Strategies.Vol_breakout.Atr_multiple
			up := hi + a*m
			dn := lo - a*m
			if closeVal > up {
				volSig = 1
			} else if closeVal < dn {
				volSig = -1
			}
			tl.Log("[alpha] vol-breakout: hi:", hi, "lo:", lo, "atr:", a, "m:", m, "up:", up, "dn:", dn, "close:", closeVal, "sig:", volSig)
		}

		carrySig := 0.0
		if c.Strategies.Carry_fx.Enabled {
			// carry_bias >0 long, <0 short, 0 neutral
			b := c.Strategies.Carry_fx.Carry_bias
			if b > 0 {
				carrySig = 1
			} else if b < 0 {
				carrySig = -1
			}
			tl.Log("[alpha] carry: bias:", b, "sig:", carrySig)
		}

		mrSig := 0.0
		if c.Strategies.Mean_reversion.Enabled {
			p := c.Strategies.Mean_reversion.Rsi_period
			if p <= 0 {
				p = 14
			}
			lower := c.Strategies.Mean_reversion.Rsi_lower
			if lower <= 0 {
				lower = 30
			}
			upper := c.Strategies.Mean_reversion.Rsi_upper
			if upper <= 0 {
				upper = 70
			}
			rsi := computeRSI(bars, p)
			if rsi > 0 {
				if rsi < lower {
					mrSig = 1
				} else if rsi > upper {
					mrSig = -1
				}
			}
			tl.Log("[alpha] mean-reversion: rsi:", rsi, "p:", p, "lo:", lower, "hi:", upper, "sig:", mrSig)
		}

		// combine
		wTrend := c.Strategies.Trend_momentum.Weight
		wVol := c.Strategies.Vol_breakout.Weight
		wCarry := c.Strategies.Carry_fx.Weight
		wMr := c.Strategies.Mean_reversion.Weight
		if wTrend <= 0 && c.Strategies.Trend_momentum.Enabled {
			wTrend = 1
		}
		if wVol <= 0 && c.Strategies.Vol_breakout.Enabled {
			wVol = 1
		}
		if wCarry == 0 && c.Strategies.Carry_fx.Enabled {
			wCarry = 1
		}
		if wMr <= 0 && c.Strategies.Mean_reversion.Enabled {
			wMr = 1
		}
		sumW := wTrend + wVol + wCarry + wMr
		score := 0.0
		combine := strings.ToLower(c.Ensemble.Combine)
		switch combine {
		case "sum":
			score = trendSig*wTrend + volSig*wVol + carrySig*wCarry + mrSig*wMr
		case "median":
			vals := []float64{}
			if c.Strategies.Trend_momentum.Enabled {
				vals = append(vals, trendSig)
			}
			if c.Strategies.Vol_breakout.Enabled {
				vals = append(vals, volSig)
			}
			if c.Strategies.Carry_fx.Enabled {
				vals = append(vals, carrySig)
			}
			if c.Strategies.Mean_reversion.Enabled {
				vals = append(vals, mrSig)
			}
			score = median(vals)
		default:
			if sumW > 0 {
				score = (trendSig*wTrend + volSig*wVol + carrySig*wCarry + mrSig*wMr) / sumW
			}
		}
		tl.Log("[alpha] ensemble combine:", combine, "weights T/V/C/M:", wTrend, wVol, wCarry, wMr, "sumW:", sumW, "score:", score)

		// apply ensemble cap/threshold
		capv := c.Ensemble.Cap
		if capv <= 0 {
			capv = 1
		}
		if score > capv {
			score = capv
		} else if score < -capv {
			score = -capv
		}
		if math.Abs(score) < maxFloat(c.Ensemble.Threshold, 0.1) {
			tl.Log("[alpha] score below threshold: |score|:", math.Abs(score), "threshold:", maxFloat(c.Ensemble.Threshold, 0.1))
			out := append([]string(nil), tl.lines...)
			tl.lines = tl.lines[:0]
			return out
		}

		sig := 0
		if score > 0 {
			sig = 1
		} else if score < 0 {
			sig = -1
		}
		if sig == 0 {
			// neutral after threshold/cap
			out := append([]string(nil), tl.lines...)
			tl.lines = tl.lines[:0]
			return out
		}
		tl.Log("[alpha] signal:", sig)

		// risk/time filters
		if shouldBlockNewTrade(o, c, mappedSymbol) {
			tl.Log("[alpha] trade blocked by risk/time filters")
			out := append([]string(nil), tl.lines...)
			tl.lines = tl.lines[:0]
			return out
		}

		// Risk-based position sizing
		var vol float32
		if strings.ToLower(c.Sizing.Mode) == "fixed" && c.Sizing.Fixed_lot > 0 {
			vol = c.Sizing.Fixed_lot
			tl.Log("[alpha] sizing fixed lot:", vol)
		} else {
			// Use risk-based sizing with ATR and account protection
			atrValue := atr.Value()
			vol = calculateRiskBasedVolume(o, c, atrValue, price)
			tl.Log("[alpha] risk-based sizing: atr:", atrValue, "price:", price, "vol:", vol)
		}

		// Apply portfolio limits
		if c.Portfolio.Per_symbol_lot_cap > 0 && float64(vol) > c.Portfolio.Per_symbol_lot_cap {
			vol = float32(c.Portfolio.Per_symbol_lot_cap)
			tl.Log("[alpha] per-symbol lot capped to:", vol)
		}

		// Safety check - ensure minimum volume
		if vol <= 0 {
			vol = 0.01 // minimum volume
			tl.Log("[alpha] volume set to minimum:", vol)
		}

		// Cap maximum volume for safety
		if vol > 0.1 {
			vol = 0.1
			tl.Log("[alpha] volume capped to maximum:", vol)
		}

		// prevent duplicate symbol position
		if hasOpenPosition(o, mappedSymbol) {
			tl.Log("[alpha] existing position found for", mappedSymbol, "skipping new trade")
			out := append([]string(nil), tl.lines...)
			tl.lines = tl.lines[:0]
			return out
		}

		// action type with optional reverse
		var actionType int64
		if sig > 0 {
			actionType = 0
		} else {
			actionType = 1
		}
		if c.Sizing.Reverse {
			if actionType == 0 {
				actionType = 1
			} else {
				actionType = 0
			}
			tl.Log("[alpha] sizing reverse applied; actionType:", actionType)
		} else {
			tl.Log("[alpha] actionType:", actionType)
		}

		// SL/TP via ATR multiples
		var slPtr, tpPtr *float32
		if strings.ToLower(c.Execution.Stop_loss.Mode) == "atr" {
			a := atr.Value()
			mult := c.Execution.Stop_loss.Atr_multiple
			if mult <= 0 {
				mult = 2.5
			}
			dist := float32(a * mult)
			if actionType == 0 {
				sl := float32(price) - dist
				slPtr = &sl
				if c.Execution.Take_profit.Rr_multiple > 0 {
					tp := float32(price) + dist*float32(c.Execution.Take_profit.Rr_multiple)
					tpPtr = &tp
				}
			} else {
				sl := float32(price) + dist
				slPtr = &sl
				if c.Execution.Take_profit.Rr_multiple > 0 {
					tp := float32(price) - dist*float32(c.Execution.Take_profit.Rr_multiple)
					tpPtr = &tp
				}
			}
			if slPtr != nil {
				tl.Log("[alpha] SL set:", *slPtr)
			}
			if tpPtr != nil {
				tl.Log("[alpha] TP set:", *tpPtr)
			}
		}

		if c.Dry_run {
			tl.Log("[alpha] dry_run enabled - would place trade; symbol:", mappedSymbol, "action:", actionType, "vol:", vol)
			out := append([]string(nil), tl.lines...)
			tl.lines = tl.lines[:0]
			return out
		}

		// optional slippage deviation and magic
		var deviation *int64
		if c.Execution.Max_slippage_points > 0 {
			d := int64(c.Execution.Max_slippage_points)
			deviation = &d
			tl.Log("[alpha] deviation points:", *deviation)
		}
		m := computeMagic(o.Id)
		ll := m // avoid shadowing in request build
		tl.Log("[alpha] computed magic:", m)
		_, err := o.Client.PlaceTrade(o.Ctx, &PlaceTradeRequest{
			Symbol:     mappedSymbol,
			ActionType: actionType,
			Volume:     vol,
			StopLoss:   slPtr,
			TakeProfit: tpPtr,
			Deviation:  deviation,
			Magic:      &ll,
		})
		if err != nil {
			tl.Log("[alpha] place trade error:", err.Error())
			out := append([]string(nil), tl.lines...)
			tl.lines = tl.lines[:0]
			return out
		}
		ll = 0
		_ = ll
		tl.Log("[alpha] PlaceTrade sent; symbol:", mappedSymbol, "action:", actionType, "vol:", vol)
		lastTradeAt = time.Now()
		actedBarTs = lastClosedTs
		ll2 := lastTradeAt.UnixMilli()
		_ = ll2
		// final state log
		// tl.Log("[alpha] state updated: lastTradeAt:", lastTradeAt.UnixMilli(), "actedBarTs:", actedBarTs)

		out := append([]string(nil), tl.lines...)
		// clear for next tick
		tl.lines = tl.lines[:0]
		return out
	}

	Initialize(&config, fn)
	println("Bot exited.")
}

// helpers
func bucketStart(t time.Time, tf string) time.Time {
	switch strings.ToUpper(tf) {
	case "M1":
		return t.Truncate(time.Minute)
	case "M5":
		return t.Truncate(5 * time.Minute)
	case "M15":
		return t.Truncate(15 * time.Minute)
	case "H1":
		return t.Truncate(time.Hour)
	default:
		return t.Truncate(5 * time.Minute)
	}
}

func annualizationFactor(tf string) float64 {
	switch strings.ToUpper(tf) {
	case "M1":
		return math.Sqrt(252 * 390) // approx minutes in US session
	case "M5":
		return math.Sqrt(252 * 78)
	case "M15":
		return math.Sqrt(252 * 26)
	case "H1":
		return math.Sqrt(252 * 6.5)
	default:
		return math.Sqrt(252 * 78)
	}
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}
func maxFloat(a, b float64) float64 {
	if a > b {
		return a
	}
	return b
}

// risk helpers (adapted from copy-slave)
func applyCurrencyMaps(symbol string, maps []string) string {
	out := symbol
	for _, m := range maps {
		parts := strings.Split(m, "->")
		if len(parts) != 2 {
			continue
		}
		from := strings.TrimSpace(parts[0])
		to := strings.TrimSpace(parts[1])
		if from == "" || to == "" {
			continue
		}
		newOut := strings.ReplaceAll(out, from, to)
		_ = newOut != out // avoid unused warning in non-logging path
		out = newOut
	}
	return out
}

// strategy helpers
func donchian(bars []Bar, n int) (float64, float64) {
	if len(bars) < n {
		return 0, 0
	}
	hi := -1e308
	lo := 1e308
	for i := len(bars) - n; i < len(bars); i++ {
		if bars[i].H > hi {
			hi = bars[i].H
		}
		if bars[i].L < lo {
			lo = bars[i].L
		}
	}
	return hi, lo
}

func computeRSI(bars []Bar, period int) float64 {
	if len(bars) < period+1 {
		return 0
	}
	var gain, loss float64
	for i := len(bars) - period; i < len(bars); i++ {
		diff := bars[i].C - bars[i-1].C
		if diff > 0 {
			gain += diff
		} else {
			loss -= diff
		}
	}
	if gain == 0 && loss == 0 {
		return 50
	}
	if loss == 0 {
		return 100
	}
	rs := gain / loss
	rsi := 100 - (100 / (1 + rs))
	if rsi < 0 {
		rsi = 0
	}
	if rsi > 100 {
		rsi = 100
	}
	return rsi
}

func median(vals []float64) float64 {
	if len(vals) == 0 {
		return 0
	}
	a := make([]float64, len(vals))
	copy(a, vals)
	for i := 1; i < len(a); i++ {
		x := a[i]
		j := i - 1
		for j >= 0 && a[j] > x {
			a[j+1] = a[j]
			j--
		}
		a[j+1] = x
	}
	m := len(a) / 2
	if len(a)%2 == 1 {
		return a[m]
	}
	return 0.5 * (a[m-1] + a[m])
}

func hasOpenPosition(o Options, symbol string) bool {
	pos, err := o.Client.GetPositions(o.Ctx, &EmptyType{})
	if err != nil || pos == nil {
		return false
	}
	for _, p := range pos.Positions {
		if strings.EqualFold(p.Symbol, symbol) {
			return true
		}
	}
	return false
}

func atoiSafe(s string) int {
	n := 0
	for _, ch := range s {
		if ch >= '0' && ch <= '9' {
			n = n*10 + int(ch-'0')
		}
	}
	return n
}

func nowWithinWindows(wins []string, now time.Time) bool {
	if len(wins) == 0 {
		return true
	}
	for _, w := range wins {
		parts := strings.Split(w, "-")
		if len(parts) != 2 {
			continue
		}
		start := strings.TrimSpace(parts[0])
		end := strings.TrimSpace(parts[1])
		if len(start) != 5 || len(end) != 5 {
			continue
		}
		sH, sM := atoiSafe(start[:2]), atoiSafe(start[3:])
		eH, eM := atoiSafe(end[:2]), atoiSafe(end[3:])
		startT := time.Date(now.Year(), now.Month(), now.Day(), sH, sM, 0, 0, now.Location())
		endT := time.Date(now.Year(), now.Month(), now.Day(), eH, eM, 0, 0, now.Location())
		if !endT.After(startT) {
			endT = endT.Add(24 * time.Hour)
			if now.Before(startT) {
				now = now.Add(24 * time.Hour)
			}
		}
		if now.Equal(startT) || (now.After(startT) && now.Before(endT)) {
			return true
		}
	}
	return false
}

var dayStart = time.Now().Format("2006-01-02")
var startEquity float32 = 0
var maxEquityToday float32 = 0

func inList(sym string, list []string) bool {
	for _, s := range list {
		if strings.EqualFold(strings.TrimSpace(s), sym) {
			return true
		}
	}
	return false
}

func shouldBlockNewTrade(o Options, cfg Config, symbol string) bool {
	if len(cfg.Risk.Symbol_whitelist) > 0 && !inList(symbol, cfg.Risk.Symbol_whitelist) {
		return true
	}
	if inList(symbol, cfg.Risk.Symbol_blacklist) {
		return true
	}
	if !nowWithinWindows(cfg.Risk.Time_windows, time.Now()) {
		return true
	}

	acct, err := o.Client.GetAccount(o.Ctx, &EmptyType{})
	if err == nil && acct != nil {
		today := time.Now().Format("2006-01-02")
		if today != dayStart {
			dayStart = today
			maxEquityToday = acct.Equity
			startEquity = acct.Equity
		}
		if acct.Equity > maxEquityToday {
			maxEquityToday = acct.Equity
		}
		if cfg.Risk.Equity_stop_abs > 0 && acct.Equity <= cfg.Risk.Equity_stop_abs {
			return true
		}
		if cfg.Risk.Equity_stop_pct > 0 && startEquity > 0 {
			if acct.Equity <= startEquity*(1.0-float32(cfg.Risk.Equity_stop_pct)/100.0) {
				return true
			}
		}
		if cfg.Risk.Max_daily_drawdown_pct > 0 && maxEquityToday > 0 {
			dd := (maxEquityToday - acct.Equity) / maxEquityToday * 100.0
			if dd >= cfg.Risk.Max_daily_drawdown_pct {
				return true
			}
		}
	}
	trades, err := o.Client.GetPositions(o.Ctx, &EmptyType{})
	if err == nil && trades != nil {
		if cfg.Risk.Max_concurrent_trades > 0 {
			if len(trades.Positions) >= cfg.Risk.Max_concurrent_trades {
				return true
			}
		}
		if cfg.Risk.Max_aggregate_lots > 0 {
			var sum float32
			for _, p := range trades.Positions {
				sum += p.Volume
			}
			if sum >= cfg.Risk.Max_aggregate_lots {
				return true
			}
		}
	}
	return false
}

// trailing stop manager: tighten stop to trail by ATR*multiple
func manageTrailing(o Options, symbol string, atrVal float64, atrMult float64, price float64) {
	if atrMult <= 0 || atrVal <= 0 {
		return
	}
	pos, err := o.Client.GetPositions(o.Ctx, &EmptyType{})
	if err != nil || pos == nil {
		return
	}
	dist := float32(atrVal * atrMult)
	for _, p := range pos.Positions {
		if !strings.EqualFold(p.Symbol, symbol) {
			continue
		}
		if p.Type == 0 { // buy
			target := float32(price) - dist
			if target > 0 && (p.StopLoss == 0 || target > p.StopLoss) {
				t := target
				_, _ = o.Client.ModifyTrade(o.Ctx, &ModifyTradeRequest{Ticket: p.Ticket, StopLoss: t})
			}
		} else if p.Type == 1 { // sell
			target := float32(price) + dist
			if target > 0 && (p.StopLoss == 0 || target < p.StopLoss) {
				t := target
				_, _ = o.Client.ModifyTrade(o.Ctx, &ModifyTradeRequest{Ticket: p.Ticket, StopLoss: t})
			}
		}
	}
}

// compute a deterministic magic number from bot id
func computeMagic(id string) int64 {
	var h int64 = 1469598103934665603
	for i := 0; i < len(id); i++ {
		h ^= int64(id[i])
		h *= 1099511628211
	}
	if h < 0 {
		h = -h
	}
	return h
}
