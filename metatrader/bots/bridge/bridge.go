package bridge

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"os"
	"sync"
	"time"

	"github.com/gorilla/websocket"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/metadata"
)

var (
	id       = flag.String("id", "", "Id of this bot executable")
	rpc_uri  = flag.String("rpc_uri", "", "Address of the gRPC server")
	ws_uri   = flag.String("ws_uri", "", "Address of the WebSocket server")
	token    = flag.String("auth_token", "", "Authentication token for the terminal")
	env_file = flag.String("env_file", "./env.json", "Path to the environment file")
)

var State *BotState = &BotState{
	Is_paused: false,
}

// in-memory logs storage and cap
var (
	logsBuffer  []string
	maxLogLines = 1000 // default cap; can be adjusted if needed
	logsMu      sync.Mutex
)

func appendLogs(lines []string) {
	// fmt.Println("appendLogs:", lines)
	if len(lines) == 0 {
		return
	}
	logsMu.Lock()
	defer logsMu.Unlock()
	logsBuffer = append(logsBuffer, lines...)
	if len(logsBuffer) > maxLogLines {
		// keep last maxLogLines
		logsBuffer = logsBuffer[len(logsBuffer)-maxLogLines:]
	}
}

func Initialize(config *Map, tick_callback Callback) (error, Options) {

	defer func() {
		if r := recover(); r != nil {
			log.Printf("Recovered from panic in callback: %v", r)
		}
	}()

	// parse flags
	flag.Parse()

	if *rpc_uri == "" || *ws_uri == "" || *token == "" || *id == "" {
		fmt.Println("Error: Missing required flags")
		flag.Usage()
		os.Exit(1)
	}

	// connect to grpc server
	conn, err := grpc.NewClient(*rpc_uri, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		log.Fatalf("did not connect: %v", err)
	}

	defer conn.Close()
	c := NewBridgeRpcServiceClient(conn)

	ctx := context.Background()

	ctx = metadata.AppendToOutgoingContext(ctx, "authorization", *token)

	// connect to websockets server
	wc, _, err := websocket.DefaultDialer.Dial(*ws_uri, nil)
	if err != nil {
		log.Fatal("dial:", err)
	}

	defer wc.Close()

	// Handle incoming messages
	done := make(chan struct{})
	go func() {
		defer close(done)
		for {
			_, message, err := wc.ReadMessage()
			if err != nil {
				log.Println("read:", err)
				return
			}
			// Parse the JSON message into a map
			var data map[string]interface{}
			if err := json.Unmarshal(message, &data); err != nil {
				log.Println("json unmarshal error:", err)
				continue
			}

			var command = data["cmd"].(string)

			switch command {
			case "START":
				State.Is_paused = false
			case "PAUSE":
				State.Is_paused = true
			case "UPDATE":
				if err := json.Unmarshal(message, &config); err != nil {
					log.Println("json unmarshal error while updating config:", err)
					continue
				}
			case "GET_STATUS":
				jsonData, err := json.Marshal(&GenericWsMessage{Event: "bot:status", Data: WebSocketData{Config: *config, State: *State, Id: *id}})

				if err != nil {
					fmt.Println("Error marshaling to JSON:", err)
					return
				}
				wc.WriteMessage(websocket.TextMessage, jsonData)
			case "GET_LOGS":
				// send current logs over ws (copy under lock)
				logsMu.Lock()
				logsCopy := append([]string(nil), logsBuffer...)
				logsMu.Unlock()
				payload, err := json.Marshal(&GenericWsMessage{Event: "bot:logs", Data: LogsMessage{Id: *id, Logs: logsCopy}})
				if err != nil {
					log.Println("error marshaling logs:", err)
					continue
				}
				wc.WriteMessage(websocket.TextMessage, payload)
			case "CLEAR_LOGS":
				logsMu.Lock()
				logsBuffer = nil
				logsMu.Unlock()
			default:
				fmt.Println("Invalid operation!")
			}
		}
	}()

	// open env file
	file, err := os.Open(*env_file)
	if err != nil {
		fmt.Println("Error opening file:", err)
	}

	defer file.Close()

	decoder := json.NewDecoder(file)
	err = decoder.Decode(&config)

	if err != nil {
		fmt.Println("Error decoding JSON:", err)
		return err, Options{}
	}

	options := Options{
		Id:        *id,
		RpcUri:    *rpc_uri,
		WsUri:     *ws_uri,
		Token:     *token,
		Client:    c,
		Ctx:       ctx,
		Simulated: false,
	}

	// send message to websockets server
	jsonData, err := json.Marshal(&GenericWsMessage{Event: "bot:init", Data: WebSocketData{Config: *config, State: *State, Id: *id}})

	if err != nil {
		fmt.Println("Error marshaling to JSON:", err)
		return err, options
	}
	print(string(jsonData))
	wc.WriteMessage(websocket.TextMessage, jsonData)

	// start ticking

	symbol := (*config)["symbol"].(string)
	rate := (*config)["rate"].(float64)
	remote_ticking := (*config)["remote_ticking"].(bool)

	// simulation/backtest mode flags
	var simulated bool
	if v, ok := (*config)["simulated"].(bool); ok {
		simulated = v
	}
	var backtestFile string
	if v, ok := (*config)["backtest_file"].(string); ok {
		backtestFile = v
	}
	var backtestCount float64
	if v, ok := (*config)["backtest_count"].(float64); ok {
		backtestCount = v
	}
	var backtestFrom int64
	if v, ok := (*config)["backtest_from"].(float64); ok {
		backtestFrom = int64(v)
	}
	var timeframe string
	if v, ok := (*config)["backtest_timeframe"].(string); ok {
		timeframe = v
	}
	var initialBalance float64 = 10000
	if v, ok := (*config)["initial_balance"].(float64); ok {
		initialBalance = v
	}
	var spread float64 = 0.0002
	if v, ok := (*config)["simulated_spread"].(float64); ok {
		spread = v
	}

	if symbol == "" {
		return fmt.Errorf("symbol is required"), options
	}

	if simulated {
		println("\n\nRunning simulated backtest...\n\n ")
		// Use simulated client and run backtest
		sim := NewSimulatedClient(float32(initialBalance), float32(spread))
		options.Client = sim
		options.Simulated = true
		// choose data source
		if backtestFile != "" {
			_ = RunBacktestFromFile(sim, backtestFile, int64(rate*1000), tick_callback, options)
			return nil, options
		}
		// else: try generating via RPC GetTicksFrom
		if backtestCount <= 0 {
			backtestCount = 1000
		}
		if backtestFrom == 0 {
			backtestFrom = time.Now().Add(-24 * time.Hour).UnixMilli()
		}

		resp, err := c.GetTicksFrom(ctx, &GetTicksFromRequest{Symbol: symbol, StartDate: backtestFrom, Length: int64(backtestCount), Timeframe: &timeframe})
		if err != nil || resp == nil {
			log.Printf("GetTicksFrom failed: %v", err)
			return err, options
		}

		// convert bars to a temp slice and feed into runner
		bars := make([]BacktestBar, 0, len(resp.Ticks))
		for _, b := range resp.Ticks {
			bars = append(bars, BacktestBar{Time: b.Time, Open: float64(b.Open), High: float64(b.High), Low: float64(b.Low), Close: float64(b.Close), Volume: float64(b.Volume)})
		}

		// write to a temp file to reuse the same runner
		tmp := fmt.Sprintf("/tmp/pairs/%s_%d_bt.json", symbol, time.Now().UnixNano())
		f, _ := os.Create(tmp)
		enc := json.NewEncoder(f)
		_ = enc.Encode(bars)
		f.Close()
		_ = RunBacktestFromFile(sim, tmp, int64(rate*1000), tick_callback, options)
		println("\n\nBacktest complete.\n\n")
		return nil, options
	}

	if remote_ticking == true {

		c.TickStop(ctx, &EmptyType{})

		var tickStream, _ = c.TickStart(ctx, &TickStartRequest{Symbol: symbol, Rate: float32(rate)})

		for {
			if State.Is_paused == true {
				continue
			}
			current_symbol := (*config)["symbol"].(string)

			if current_symbol != symbol {
				c.TickStop(ctx, &EmptyType{})
				tickStream, _ = c.TickStart(ctx, &TickStartRequest{Symbol: current_symbol, Rate: float32(rate)})
				symbol = current_symbol
			}
			res, err := tickStream.Recv()

			if err != nil {
				log.Printf("Error receiving stream: %v", err)
			}
			// execute callback and collect logs
			lines := tick_callback(options, res)
			appendLogs(lines)
		}
	} else {
		for {
			if State.Is_paused == true {
				continue
			}
			lines := tick_callback(options, nil)
			appendLogs(lines)
			time.Sleep(time.Duration(rate) * time.Second)
		}
	}

}
