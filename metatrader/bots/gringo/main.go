package main

import (
	. "bridge"
	"fmt"
	"math"
	"strings"
	"time"
)

type tickLogger struct {
	lines []string
	max   int
}

func newTickLogger(max int) *tickLogger { return &tickLogger{lines: make([]string, 0, 128), max: max} }

func (l *tickLogger) Log(args ...interface{}) {
	// fmt.Println("tickLogger.Log:", args)
	if l == nil {
		return
	}
	msg := fmt.Sprintln(args...)
	l.lines = append(l.lines, strings.TrimRight(msg, "\n"))
	if len(l.lines) > l.max {
		l.lines = l.lines[len(l.lines)-l.max:]
	}
}

type Config struct {
	Symbol                string
	Rate                  float64
	Remote_ticking        bool
	Currency_maps         []string
	Dry_run               bool
	Min_trade_interval_ms int

	Scalping struct {
		Timeframe          string  // M1, M5
		Fast_ema_period    int     // 5
		Slow_ema_period    int     // 21
		Rsi_period         int     // 14
		Rsi_oversold       float64 // 30
		Rsi_overbought     float64 // 70
		Bb_period          int     // 20
		Bb_deviation       float64 // 2.0
		Min_pip_move       float64 // 0.0005 for EURUSD
		Max_spread_pips    float64 // 2.0
		Quick_profit_pips  float64 // 3-5 pips
		Stop_loss_pips     float64 // 8-10 pips
		Risk_per_trade_pct float64 // 1%
		Fixed_volume       float32 // 0.01
	}

	Risk struct {
		Max_concurrent_trades    int
		Max_daily_drawdown_pct   float64
		Max_daily_trades         int
		Time_windows             []string
		Stop_trading_on_loss_pct float64
	}

	Execution struct {
		Max_slippage_points int
		Magic_base          int
	}
}

var config Map = Map{}

// Scalping indicators
type EMA struct {
	period int
	alpha  float64
	value  float64
	init   bool
}

func NewEMA(period int) *EMA {
	return &EMA{period: period, alpha: 2.0 / (float64(period) + 1.0)}
}

func (e *EMA) Add(price float64) {
	if !e.init {
		e.value = price
		e.init = true
	} else {
		e.value = e.alpha*price + (1-e.alpha)*e.value
	}
}

func (e *EMA) Value() float64 { return e.value }

type RSI struct {
	period    int
	gains     []float64
	losses    []float64
	avgGain   float64
	avgLoss   float64
	lastPrice float64
	init      bool
}

func NewRSI(period int) *RSI {
	return &RSI{period: period}
}

func (r *RSI) Add(price float64) {
	if !r.init {
		r.lastPrice = price
		r.init = true
		return
	}

	change := price - r.lastPrice
	gain := math.Max(change, 0)
	loss := math.Max(-change, 0)

	r.gains = append(r.gains, gain)
	r.losses = append(r.losses, loss)

	if len(r.gains) > r.period {
		r.gains = r.gains[1:]
		r.losses = r.losses[1:]
	}

	if len(r.gains) == r.period {
		sumGains := 0.0
		sumLosses := 0.0
		for i := 0; i < r.period; i++ {
			sumGains += r.gains[i]
			sumLosses += r.losses[i]
		}
		r.avgGain = sumGains / float64(r.period)
		r.avgLoss = sumLosses / float64(r.period)
	}

	r.lastPrice = price
}

func (r *RSI) Value() float64 {
	if r.avgLoss == 0 {
		return 100
	}
	rs := r.avgGain / r.avgLoss
	return 100 - (100 / (1 + rs))
}

type BollingerBands struct {
	period    int
	deviation float64
	prices    []float64
	sma       float64
	upper     float64
	lower     float64
}

func NewBollingerBands(period int, deviation float64) *BollingerBands {
	return &BollingerBands{period: period, deviation: deviation}
}

func (bb *BollingerBands) Add(price float64) {
	bb.prices = append(bb.prices, price)
	if len(bb.prices) > bb.period {
		bb.prices = bb.prices[1:]
	}

	if len(bb.prices) == bb.period {
		sum := 0.0
		for _, p := range bb.prices {
			sum += p
		}
		bb.sma = sum / float64(bb.period)

		variance := 0.0
		for _, p := range bb.prices {
			variance += math.Pow(p-bb.sma, 2)
		}
		stdDev := math.Sqrt(variance / float64(bb.period))

		bb.upper = bb.sma + bb.deviation*stdDev
		bb.lower = bb.sma - bb.deviation*stdDev
	}
}

func (bb *BollingerBands) Upper() float64  { return bb.upper }
func (bb *BollingerBands) Lower() float64  { return bb.lower }
func (bb *BollingerBands) Middle() float64 { return bb.sma }

// Bar aggregation
type Bar struct {
	O, H, L, C float64
	T          int64
}

func bucketStart(t time.Time, timeframe string) time.Time {
	switch timeframe {
	case "M1":
		return time.Date(t.Year(), t.Month(), t.Day(), t.Hour(), t.Minute(), 0, 0, t.Location())
	case "M5":
		min := (t.Minute() / 5) * 5
		return time.Date(t.Year(), t.Month(), t.Day(), t.Hour(), min, 0, 0, t.Location())
	case "M15":
		min := (t.Minute() / 15) * 15
		return time.Date(t.Year(), t.Month(), t.Day(), t.Hour(), min, 0, 0, t.Location())
	default:
		return time.Date(t.Year(), t.Month(), t.Day(), t.Hour(), t.Minute(), 0, 0, t.Location())
	}
}

func applyCurrencyMaps(symbol string, maps []string) string {
	for _, m := range maps {
		parts := strings.Split(m, ":")
		if len(parts) == 2 && parts[0] == symbol {
			return parts[1]
		}
	}
	return symbol
}

func computeMagic(botId string) int64 {
	hash := int64(0)
	for _, c := range botId {
		hash = hash*31 + int64(c)
	}
	return 900000 + (hash % 100000)
}

func main() {
	tl := newTickLogger(500)
	tl.Log("[gringo] Starting scalping bot...")

	// Trading state
	lastTradeAt := time.Time{}
	var actedBarTs int64 = 0
	dailyTrades := 0
	dayStart := ""

	// Bar aggregation
	var curBar *Bar
	var bars []Bar
	var initialized bool = false

	// Indicators - will be initialized after fetching historical data
	var fastEMA *EMA
	var slowEMA *EMA
	var rsi *RSI
	var bb *BollingerBands

	// Function to initialize indicators with historical data
	initializeIndicators := func(o Options, c Config) error {
		if initialized {
			return nil
		}

		symbol := c.Symbol
		if symbol == "" {
			symbol = "EURUSD"
		}
		mappedSymbol := applyCurrencyMaps(symbol, c.Currency_maps)

		timeframe := c.Scalping.Timeframe
		if timeframe == "" {
			timeframe = "M15"
		}

		// In simulation mode, skip historical fetch as it's not needed
		if o.Simulated {
			tl.Log("[gringo] Simulation mode - initializing indicators without historical data")
			fastEMA = NewEMA(c.Scalping.Fast_ema_period)
			slowEMA = NewEMA(c.Scalping.Slow_ema_period)
			rsi = NewRSI(c.Scalping.Rsi_period)
			bb = NewBollingerBands(c.Scalping.Bb_period, c.Scalping.Bb_deviation)
			initialized = true
			return nil
		}

		// Fetch last 50 candles for indicator warmup (live mode only)
		tl.Log("[gringo] Fetching historical candles for warmup...")
		resp, err := o.Client.GetTicksFrom(o.Ctx, &GetTicksFromRequest{
			Symbol:    mappedSymbol,
			StartDate: time.Now().UnixMilli(),
			Length:    50,
			Timeframe: &timeframe,
		})

		if err != nil || resp == nil || len(resp.Ticks) < 10 {
			tl.Log("[gringo] Failed to fetch historical data, using defaults")
			// Initialize with default periods from config
			fastEMA = NewEMA(c.Scalping.Fast_ema_period)
			slowEMA = NewEMA(c.Scalping.Slow_ema_period)
			rsi = NewRSI(c.Scalping.Rsi_period)
			bb = NewBollingerBands(c.Scalping.Bb_period, c.Scalping.Bb_deviation)
			initialized = true
			return nil
		}

		// Initialize indicators
		fastEMA = NewEMA(c.Scalping.Fast_ema_period)
		slowEMA = NewEMA(c.Scalping.Slow_ema_period)
		rsi = NewRSI(c.Scalping.Rsi_period)
		bb = NewBollingerBands(c.Scalping.Bb_period, c.Scalping.Bb_deviation)

		// Warm up indicators with historical data
		tl.Log("[gringo] Warming up indicators with", len(resp.Ticks), "historical candles")
		for _, candle := range resp.Ticks {
			price := float64(candle.Close)
			fastEMA.Add(price)
			slowEMA.Add(price)
			rsi.Add(price)
			bb.Add(price)

			// Also populate bars array
			bars = append(bars, Bar{
				O: float64(candle.Open),
				H: float64(candle.High),
				L: float64(candle.Low),
				C: float64(candle.Close),
				T: candle.Time,
			})
		}

		tl.Log("[gringo] Indicators initialized with", len(bars), "historical bars")
		tl.Log("[gringo] Fast EMA:", fastEMA.Value(), "Slow EMA:", slowEMA.Value(), "RSI:", rsi.Value())
		initialized = true
		return nil
	}

	var fn Callback = func(o Options, tick *TickType) []string {
		tl.lines = tl.lines[:0]
		tl.Log("[gringo] callback invoked")

		c := Config{}
		MapToStruct(config, &c)

		// Initialize indicators on first call
		if !initialized {
			if err := initializeIndicators(o, c); err != nil {
				tl.Log("[gringo] Failed to initialize indicators:", err)
			}
		}

		if tick == nil {
			tl.Log("[gringo] tick is nil, returning")
			out := append([]string(nil), tl.lines...)
			tl.lines = tl.lines[:0]
			return out
		}

		tl.Log("[gringo] tick:", tick.Time, "ask:", tick.Ask, "bid:", tick.Bid)

		// Throttle trades
		if !lastTradeAt.IsZero() && o.Simulated == false && time.Since(lastTradeAt) < time.Duration(c.Min_trade_interval_ms)*time.Millisecond {
			tl.Log("[gringo] throttled, lastTradeAt:", lastTradeAt.UnixMilli())
			out := append([]string(nil), tl.lines...)
			tl.lines = tl.lines[:0]
			return out
		}

		symbol := c.Symbol
		if symbol == "" {
			symbol = "EURUSD"
		}
		mappedSymbol := applyCurrencyMaps(symbol, c.Currency_maps)

		// Aggregate ticks into bars
		price := float64((tick.Ask + tick.Bid) / 2)
		ts := time.UnixMilli(tick.Time)
		timeframe := c.Scalping.Timeframe
		if timeframe == "" {
			timeframe = "M1"
		}
		bucket := bucketStart(ts, timeframe)

		if curBar == nil || curBar.T != bucket.UnixMilli() {
			if curBar != nil {
				// Finalize previous bar and update indicators
				bars = append(bars, *curBar)
				if len(bars) > 1000 {
					bars = bars[len(bars)-500:]
				}

				fastEMA.Add(curBar.C)
				slowEMA.Add(curBar.C)
				rsi.Add(curBar.C)
				bb.Add(curBar.C)

				tl.Log("[gringo] closed bar:", curBar.T, "OHLC:", curBar.O, curBar.H, curBar.L, curBar.C)
			}
			// Start new bar
			curBar = &Bar{O: price, H: price, L: price, C: price, T: bucket.UnixMilli()}
		} else {
			// Update current bar
			if price > curBar.H {
				curBar.H = price
			}
			if price < curBar.L {
				curBar.L = price
			}
			curBar.C = price
		}

		// Check if indicators are ready
		if !initialized || fastEMA == nil || slowEMA == nil || rsi == nil || bb == nil {
			tl.Log("[gringo] indicators not ready yet")
			out := append([]string(nil), tl.lines...)
			tl.lines = tl.lines[:0]
			return out
		}

		// Daily reset
		today := time.Now().Format("2006-01-02")
		if today != dayStart {
			dayStart = today
			dailyTrades = 0
			tl.Log("[gringo] new day, reset counters")
		}

		// Risk checks
		if c.Risk.Max_daily_trades > 0 && dailyTrades >= c.Risk.Max_daily_trades {
			tl.Log("[gringo] max daily trades reached:", dailyTrades)
			out := append([]string(nil), tl.lines...)
			tl.lines = tl.lines[:0]
			return out
		}

		// Check spread
		spread := float64(tick.Ask - tick.Bid)
		maxSpread := c.Scalping.Max_spread_pips
		if maxSpread == 0 {
			maxSpread = 0.0002 // 2 pips for EURUSD
		}
		if spread > maxSpread {
			tl.Log("[gringo] spread too wide:", spread, "max:", maxSpread)
			out := append([]string(nil), tl.lines...)
			tl.lines = tl.lines[:0]
			return out
		}

		// Get current positions
		positions, err := o.Client.GetPositions(o.Ctx, &EmptyType{})
		if err != nil {
			tl.Log("[gringo] failed to get positions:", err)
			out := append([]string(nil), tl.lines...)
			tl.lines = tl.lines[:0]
			return out
		}

		hasPosition := false
		for _, p := range positions.Positions {
			if strings.EqualFold(p.Symbol, mappedSymbol) {
				hasPosition = true
				break
			}
		}

		// Only trade on new bars to avoid over-trading
		if len(bars) == 0 {
			tl.Log("[gringo] no bars available yet")
			out := append([]string(nil), tl.lines...)
			tl.lines = tl.lines[:0]
			return out
		}

		lastClosedTs := bars[len(bars)-1].T
		if actedBarTs == lastClosedTs {
			out := append([]string(nil), tl.lines...)
			tl.lines = tl.lines[:0]
			return out
		}

		// SCALPING STRATEGIES

		// Strategy 1: EMA Crossover with RSI Filter
		fastVal := fastEMA.Value()
		slowVal := slowEMA.Value()
		rsiVal := rsi.Value()

		emaBullish := fastVal > slowVal
		emaBearish := fastVal < slowVal
		rsiOversold := rsiVal < (c.Scalping.Rsi_oversold + 10)     // 40 instead of 30 for scalping
		rsiOverbought := rsiVal > (c.Scalping.Rsi_overbought - 10) // 60 instead of 70

		// Strategy 2: Bollinger Band Mean Reversion
		bbUpper := bb.Upper()
		bbLower := bb.Lower()
		bbMiddle := bb.Middle()

		tl.Log("[gringo] indicators: EMA fast=", fastVal, "slow=", slowVal, "RSI=", rsiVal, "BB=", bbLower, "/", bbMiddle, "/", bbUpper, "price=", price)

		nearUpperBand := price > (bbUpper - (bbUpper-bbMiddle)*0.2)
		nearLowerBand := price < (bbLower + (bbMiddle-bbLower)*0.2)

		// Strategy 3: Price Action - Quick momentum
		recentBars := 3
		if len(bars) >= recentBars+1 { // Need at least recentBars+1 bars for momentum calculation
			momentum := bars[len(bars)-1].C - bars[len(bars)-recentBars].C
			minMove := c.Scalping.Min_pip_move
			if minMove == 0 {
				minMove = 0.0005 // 0.5 pips
			}

			strongBullMomentum := momentum > minMove*2
			strongBearMomentum := momentum < -minMove*2

			// ENTRY SIGNALS
			var signal string
			var actionType int64

			// Long signals
			if !hasPosition && ((emaBullish && rsiOversold) || // EMA cross + RSI
				(nearLowerBand && rsiOversold) || // BB mean reversion
				(strongBullMomentum && rsiVal < 60)) { // Momentum + not overbought
				signal = "LONG"
				actionType = 0
			}

			// Short signals
			if !hasPosition && ((emaBearish && rsiOverbought) || // EMA cross + RSI
				(nearUpperBand && rsiOverbought) || // BB mean reversion
				(strongBearMomentum && rsiVal > 40)) { // Momentum + not oversold
				signal = "SHORT"
				actionType = 1
			}

			if signal != "" {
				tl.Log("[gringo] SIGNAL:", signal, "EMA:", fastVal, "/", slowVal, "RSI:", rsiVal, "BB:", bbLower, "/", bbUpper, "Price:", price)

				if c.Dry_run {
					tl.Log("[gringo] DRY RUN - would place", signal, "trade")
				} else {
					// Calculate position size and levels
					vol := calculateVolume(o, c, mappedSymbol)
					if vol > 0 {
						stopPips := c.Scalping.Stop_loss_pips
						if stopPips == 0 {
							stopPips = 10 // 10 pips default
						}
						profitPips := c.Scalping.Quick_profit_pips
						if profitPips == 0 {
							profitPips = 5 // 5 pips default
						}

						// Calculate pip value (0.0001 for most pairs, 0.01 for JPY pairs)
						pipValue := 0.0001
						if strings.Contains(mappedSymbol, "JPY") {
							pipValue = 0.01
						}

						var sl, tp *float32
						if actionType == 0 { // Long
							slPrice := float32(price - stopPips*pipValue)
							tpPrice := float32(price + profitPips*pipValue)
							sl = &slPrice
							tp = &tpPrice
							tl.Log("[gringo] LONG levels: entry=", price, "SL=", slPrice, "TP=", tpPrice)
						} else { // Short
							slPrice := float32(price + stopPips*pipValue)
							tpPrice := float32(price - profitPips*pipValue)
							sl = &slPrice
							tp = &tpPrice
							tl.Log("[gringo] SHORT levels: entry=", price, "SL=", slPrice, "TP=", tpPrice)
						}

						magic := computeMagic(o.Id)
						_, err := o.Client.PlaceTrade(o.Ctx, &PlaceTradeRequest{
							Symbol:     mappedSymbol,
							ActionType: actionType,
							Volume:     vol,
							StopLoss:   sl,
							TakeProfit: tp,
							Magic:      &magic,
						})

						if err != nil {
							tl.Log("[gringo] trade error:", err.Error())
						} else {
							tl.Log("[gringo] placed", signal, "trade vol:", vol, "SL:", *sl, "TP:", *tp)
							lastTradeAt = time.Now()
							actedBarTs = lastClosedTs
							dailyTrades++
						}
					}
				}
			}
		}

		out := append([]string(nil), tl.lines...)
		tl.lines = tl.lines[:0]
		return out
	}
	Initialize(&config, fn)
}

func calculateVolume(_ Options, c Config, _ string) float32 {
	if c.Scalping.Fixed_volume > 0 {
		return c.Scalping.Fixed_volume
	}
	// Use larger volume for more visible P&L in backtests
	return 0.1 // 0.01 lots = 1000 units for forex
}
